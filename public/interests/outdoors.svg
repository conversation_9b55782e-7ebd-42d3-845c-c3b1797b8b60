<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f7931e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d62d20;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87ceeb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffa500;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky background -->
  <rect width="400" height="300" fill="url(#skyGradient)"/>
  
  <!-- Mountains -->
  <polygon points="0,200 100,80 200,120 300,60 400,100 400,300 0,300" fill="url(#mountainGradient)"/>
  <polygon points="50,220 150,100 250,140 350,80 400,120 400,300 0,300" fill="#cc5500" opacity="0.8"/>
  
  <!-- Trees -->
  <ellipse cx="80" cy="240" rx="15" ry="25" fill="#2d5016"/>
  <ellipse cx="120" cy="250" rx="12" ry="20" fill="#2d5016"/>
  <ellipse cx="280" cy="230" rx="18" ry="30" fill="#2d5016"/>
  <ellipse cx="320" cy="245" rx="14" ry="22" fill="#2d5016"/>
  
  <!-- Sun -->
  <circle cx="350" cy="50" r="25" fill="#ffeb3b" opacity="0.9"/>
</svg>
