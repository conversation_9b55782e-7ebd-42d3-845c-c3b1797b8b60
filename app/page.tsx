import Navbar from './components/layouts/Navbar';
import HeroSection from './components/sections/Hero';
import OfficialGuideSection from './components/sections/OfficialGuideSection';
import InterestSection from './components/sections/InterestSection';
import TreksSection from './components/sections/TreksSection';
import ReviewsSection from './components/sections/ReviewsSection';
import FooterSection from './components/sections/FooterSection';

export default function Home() {
  return (
    <div className="w-full">
      {/* Navbar - Full width */}
      <Navbar />

      {/* Main content container with max-width 80vw */}
      <div className="max-w-[80vw] mx-auto">
        <HeroSection />
      </div>

      {/* Official Guide Section - Full width */}
      <OfficialGuideSection />

      {/* Interest Section - Full width */}
      <InterestSection />

      {/* Main content container with max-width 80vw */}
      <div className="max-w-[80vw] mx-auto">
        <TreksSection />
        <ReviewsSection />
      </div>

      {/* Footer - Full width */}
      <FooterSection />
    </div>
  );
}