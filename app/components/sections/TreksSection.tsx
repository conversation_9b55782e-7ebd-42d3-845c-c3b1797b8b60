"use client"
import { useState } from 'react';
import TrekCard from '../ui/TrekCard';

interface Trek {
  id: number;
  type: string;
  title: string;
  duration: string;
  groupSize?: string;
  pickupAvailable: boolean;
  rating: number;
  reviewCount: number;
  originalPrice?: number;
  currentPrice: number;
  priceUnit: string;
  image: string;
}

const TreksSection = () => {
  const [favorites, setFavorites] = useState<Set<number>>(new Set());

  const toggleFavorite = (id: number) => {
    setFavorites(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const treks: Trek[] = [
    {
      id: 1,
      type: "DAY TRIP",
      title: "From Marrakech: Ouzoud Waterfalls Guided Hike and Boat Trip",
      duration: "10 hours",
      groupSize: "Small group",
      pickupAvailable: true,
      rating: 4.7,
      reviewCount: 28059,
      originalPrice: 282,
      currentPrice: 211,
      priceUnit: "per person",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: 2,
      type: "DAY TRIP",
      title: "Marrakesh: Ourika Waterfalls, Atlas Mountains, Guide + Lunch",
      duration: "7 - 8 hours",
      pickupAvailable: true,
      rating: 4.9,
      reviewCount: 501,
      originalPrice: 190,
      currentPrice: 95,
      priceUnit: "per person",
      image: "https://images.unsplash.com/photo-1464822759844-d150baec58d1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: 3,
      type: "DAY TRIP",
      title: "Marrakech: Ourika Valley, Lunch, & Guided Hike to Waterfalls",
      duration: "8 hours",
      groupSize: "Skip the line",
      pickupAvailable: true,
      rating: 4.9,
      reviewCount: 1130,
      originalPrice: 279,
      currentPrice: 187,
      priceUnit: "per person",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: 4,
      type: "GUIDED TOUR",
      title: "Rome Driving Tour By Vintage Fiat 500",
      duration: "1.5 - 3 hours",
      pickupAvailable: false,
      rating: 4.9,
      reviewCount: 231,
      currentPrice: 1637,
      priceUnit: "per person",
      image: "https://images.unsplash.com/photo-1552832230-c0197dd311b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: 5,
      type: "MULTI-DAY TOUR",
      title: "3-Day Sahara Desert Trek from Marrakech",
      duration: "3 days",
      groupSize: "Small group",
      pickupAvailable: true,
      rating: 4.8,
      reviewCount: 892,
      originalPrice: 450,
      currentPrice: 299,
      priceUnit: "per person",
      image: "https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: 6,
      type: "ADVENTURE TOUR",
      title: "High Atlas Mountains: 2-Day Toubkal Summit Trek",
      duration: "2 days",
      groupSize: "Small group",
      pickupAvailable: true,
      rating: 4.6,
      reviewCount: 456,
      originalPrice: 380,
      currentPrice: 245,
      priceUnit: "per person",
      image: "https://images.unsplash.com/photo-1464822759844-d150baec58d1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    }
  ];

  return (
    <section className="py-8 sm:py-16 lg:py-20 bg-white">
      <div className="w-full mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
        {/* Section Header */}
        <div className="mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Continue planning
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl">
            Discover amazing treks and adventures in Morocco's most beautiful destinations
          </p>
        </div>

        {/* Trek Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {treks.map((trek) => (
            <TrekCard
              key={trek.id}
              trek={trek}
              isFavorite={favorites.has(trek.id)}
              onToggleFavorite={toggleFavorite}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TreksSection;