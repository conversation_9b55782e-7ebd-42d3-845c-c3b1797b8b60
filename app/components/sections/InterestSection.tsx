"use client"
import React from "react";

const InterestSection = () => {
  // Interest categories with online images
  const interests = [
    {
      title: "Outdoors",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-orange-600/60 to-red-700/60",
    },
    {
      title: "Food",
      image: "https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-amber-600/60 to-orange-700/60",
    },
    {
      title: "Culture",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-blue-600/60 to-purple-700/60",
    },
    {
      title: "Water",
      image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-teal-600/60 to-emerald-700/60",
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-[80vw] mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-left mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
            Find things to do by interest
          </h2>
          <p className="text-lg text-gray-600">
            Whatever you're into, we've got it
          </p>
        </div>

        {/* Interest Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {interests.map((interest, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-2xl aspect-[4/3] min-h-[200px] cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl bg-gray-200"
            >
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-300 group-hover:scale-110 bg-gray-300"
                style={{
                  backgroundImage: `url('${interest.image}')`,
                  backgroundColor: '#d1d5db' // fallback color
                }}
              />

              {/* Gradient Overlay */}
              <div
                className={`absolute inset-0 bg-gradient-to-t ${interest.gradient} transition-opacity duration-300 group-hover:opacity-90`}
              />

              {/* Content */}
              <div className="absolute inset-0 flex items-end p-6">
                <h3 className="text-white text-2xl font-bold drop-shadow-lg">
                  {interest.title}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrow (Optional) */}
        <div className="flex justify-end mt-8">
          <button className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-shadow duration-300 group">
            <svg
              className="w-6 h-6 text-gray-600 group-hover:text-gray-800 transition-colors duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default InterestSection;
