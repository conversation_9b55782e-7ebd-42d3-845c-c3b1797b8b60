"use client"
import React from "react";

const InterestSection = () => {
  // Trekking categories with online images
  const treks = [
    {
      title: "Hiking",
      image: "https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-green-700/70 to-lime-700/70",
    },
    {
      title: "Day Trek",
      image: "https://images.unsplash.com/photo-1464822759844-d150baec0494?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-blue-700/70 to-cyan-700/70",
    },
    {
      title: "Multi-day Trek",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-purple-700/70 to-pink-700/70",
    },
    {
      title: "Mountain Trek",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      gradient: "from-gray-800/70 to-zinc-700/70",
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-[80vw] mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Explore Trekking Categories
          </h2>
          <p className="text-lg text-gray-600">
            Choose your preferred trekking experience
          </p>
        </div>

        {/* Trek Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {treks.map((trek, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-3xl aspect-[16/10] min-h-[250px] cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl bg-gray-200"
              style={{ maxWidth: "400px", margin: "0 auto" }}
            >
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-300 group-hover:scale-110 bg-gray-300"
                style={{
                  backgroundImage: `url('${trek.image}')`,
                  backgroundColor: '#d1d5db' // fallback color
                }}
              />

              {/* Gradient Overlay */}
              <div
                className={`absolute inset-0 bg-gradient-to-t ${trek.gradient} transition-opacity duration-300 group-hover:opacity-90`}
              />

              {/* Content */}
              <div className="absolute inset-0 flex items-end p-6">
                <h3 className="text-white text-2xl font-bold drop-shadow-lg">
                  {trek.title}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrow (Optional) */}
        <div className="flex justify-end mt-8">
          <button className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-shadow duration-300 group">
            <svg
              className="w-6 h-6 text-gray-600 group-hover:text-gray-800 transition-colors duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default InterestSection;
