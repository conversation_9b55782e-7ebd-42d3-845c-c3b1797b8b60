"use client"
import React from 'react';

const InterestSection = () => {
  const interests = [
    {
      title: 'Outdoors',
      image: '/interests/outdoors.svg',
      gradient: 'from-orange-600/70 to-red-700/70'
    },
    {
      title: 'Food',
      image: '/interests/food.svg',
      gradient: 'from-amber-600/70 to-orange-700/70'
    },
    {
      title: 'Culture',
      image: '/interests/culture.svg',
      gradient: 'from-blue-600/70 to-purple-700/70'
    },
    {
      title: 'Water',
      image: '/interests/water.svg',
      gradient: 'from-teal-600/70 to-emerald-700/70'
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-[80vw] mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Find things to do by interest
          </h2>
          <p className="text-lg text-gray-600">
            Whatever you're into, we've got it
          </p>
        </div>

        {/* Interest Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {interests.map((interest, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-2xl aspect-[4/3] cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-300 group-hover:scale-110"
                style={{
                  backgroundImage: `url('${interest.image}')`
                }}
              />
              
              {/* Gradient Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-t ${interest.gradient} transition-opacity duration-300 group-hover:opacity-80`} />
              
              {/* Content */}
              <div className="absolute inset-0 flex items-end p-6">
                <h3 className="text-white text-xl md:text-2xl font-bold">
                  {interest.title}
                </h3>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrow (Optional - matching the design) */}
        <div className="flex justify-end mt-8">
          <button className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-shadow duration-300 group">
            <svg 
              className="w-6 h-6 text-gray-600 group-hover:text-gray-800 transition-colors duration-300" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default InterestSection;
